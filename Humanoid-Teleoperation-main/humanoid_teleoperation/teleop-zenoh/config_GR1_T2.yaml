device_connected: true
cpu: "X64"
system: "LINUX"
robot:
  name: "GR1"
  mechanism: "T2"
  control_period: 0.01
  input_and_calculate_period: 0

  # right now, the state estimator is not very accurate, suggest to disable it
  # unless you know what you are doing
  state_estimator:
    enable: false
    path: "/state_estimator/model_gr1_t2.json"

comm:
  enable: false
  use_raw: false
  use_json: false
  use_dds: false
  use_sim: false

operator:
  use_joystick: false
  joystick_connected: false
  joystick_type: "XBOX"  # support "XBOS", "PS4", "PS5"
  use_keyboard: false
  keyboard_connected: false

ota:
  enable: false

hardware:
  use_can: false
  use_ethernet: false
  use_etherbus: false
  use_fi_fse: true
  use_fi_fsa: true

sensor_usb_imu:
  usb: [
    "/dev/ttyUSB0"
  ]
  comm_enable: [
    false
  ]
  comm_frequency: [
    500
  ]

sensor_abs_encoder:
  type: "FIFSEV1"
  ip: [
    # left leg
    "***************", "***************", "***************", "***************", "***************", "***************",
    # right leg
    "***************", "***************", "***************", "***************", "***************", "***************",
    # waist
    "***************", "***************", "***************",
  ]
  data_path: "sensor_offset.json"

fi_fse:
  version: "v1"

fi_fsa:
  version: "v1"
  debug: false
  blocking: false
  timeout: 0.01
  port_ctrl: 2333
  port_comm: 2334
  port_fast: 2335
  network: "***************"
  concurrency: true

actuator:
  type: "FIFSAV1"
  use_dic: false
  names: [
      "l_hip_roll", "l_hip_yaw", "l_hip_pitch", "l_knee_pitch", "l_ankle_pitch", "l_ankle_roll",
      "r_hip_roll", "r_hip_yaw", "r_hip_pitch", "r_knee_pitch", "r_ankle_pitch", "r_ankle_roll",
      "joint_waist_yaw", "joint_waist_pitch", "joint_waist_roll",
      "joint_head_pitch", "joint_head_roll", "joint_head_yaw",
      "l_shoulder_pitch", "l_shoulder_roll", "l_shoulder_yaw", "l_elbow_pitch", "l_wrist_yaw", "l_wrist_roll", "l_wrist_pitch",
      "r_shoulder_pitch", "r_shoulder_roll", "r_shoulder_yaw", "r_elbow_pitch", "r_wrist_yaw", "r_wrist_roll", "r_wrist_pitch",
  ]
  ip: [
    # left leg
    "**************", "**************", "**************", "**************", "**************", "**************",
    # right leg
    "**************", "**************", "**************", "**************", "**************", "**************",
    # waist
    "**************", "**************", "**************",
    # head
    "**************", "**************", "**************",
    # left arm
    "**************" , "**************" , "**************" , "**************" , "**************" , "**************" , "**************",
    # right arm
    "**************" , "**************" , "**************" , "**************" , "**************" , "**************" , "**************",
  ]
  comm_enable: [
    # left leg
    false, false, false, false, false, false,
    # right leg
    false, false, false, false, false, false,
    # waist
    true, true, true,
    # head
    true, true, true,
    # left arm
    true, true, true, true, true, true, true,
    # right arm
    true, true, true, true, true, true, true,
  ]
  comm_use_fast: [
    # left leg
    true, true, true, true, true, true,
    # right leg
    true, true, true, true, true, true,
    # waist
    true, true, true,
    # head
    true, true, true,
    # left arm
    true, true, true, true, true, true, true,
    # right arm
    true, true, true, true, true, true, true,
  ]
